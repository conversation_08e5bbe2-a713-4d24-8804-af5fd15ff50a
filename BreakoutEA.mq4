//+------------------------------------------------------------------+
//|                                                    BreakoutEA.mq4 |
//|                                  Copyright 2024, MetaQuotes Ltd. |
//|                                             https://www.mql5.com |
//+------------------------------------------------------------------+
#property copyright "Copyright 2024"
#property link      ""
#property version   "1.00"
#property strict

// 输入参数
input int    LookbackBars = 10;        // 回看柱数
input int    MinRange = 2500;          // 最小波动范围(点)
input int    BreakoutDistance = 100;   // 突破距离(点)
input int    StopLossBars = 3;         // 止损参考柱数
input int    StopLossOffset = 100;     // 止损偏移(点)
input int    TakeProfit = 2000;        // 止盈(点)
input int    TrailingProfit = 200;     // 移动止损盈利触发(点，0=不限制)
input int    TrailingOffset = 50;      // 移动止损偏移(点)
input bool   EnableTrailing = true;    // 启用移动止损
input bool   EnableRecovery = true;    // 启用止损触发回本功能
input int    RecoveryStopBars = 3;     // 回本单止损参考柱数
input bool   EnableRecoveryTP = true;  // 启用回本单止盈
input bool   EnableCooldown = true;    // 启用间歇开单功能
input int    CooldownBars = 4;         // 盈利平仓后冷却K线数量
input bool   EnableSameBarBlock = true; // 启用同K线盈利平仓阻止开单功能
input double LotSize = 0.1;            // 手数

// 全局变量
int magicNumber = 12345;
int recoveryMagicNumber = 54321;  // 回本单魔术号码
datetime lastBarTime = 0;

// 冷却功能相关变量
bool isInCooldown = false;        // 是否处于冷却期
int cooldownBarsCount = 0;        // 已冷却的K线数量

// 同K线盈利平仓阻止开单功能变量
bool hasProfitCloseThisBar = false;  // 当前K线是否有盈利平仓
datetime currentBarTime = 0;         // 当前K线时间

// 策略单和回本单跟踪
struct OrderInfo
{
   int ticket;
   double openPrice;
   double lotSize;
   int orderType;
   bool hasRecoveryOrder;
   int recoveryTicket;
};

OrderInfo strategyOrder;  // 当前策略单信息

int OnInit()
{
   // 参数验证
   if(!ValidateInputParameters())
   {
      return(INIT_PARAMETERS_INCORRECT);
   }

   Print("突破EA初始化完成");

   // 初始化策略单信息
   strategyOrder.ticket = -1;
   strategyOrder.openPrice = 0;
   strategyOrder.lotSize = 0;
   strategyOrder.orderType = -1;
   strategyOrder.hasRecoveryOrder = false;
   strategyOrder.recoveryTicket = -1;

   // 初始化冷却功能
   isInCooldown = false;
   cooldownBarsCount = 0;

   // 初始化同K线盈利平仓阻止开单功能变量
   hasProfitCloseThisBar = false;
   currentBarTime = Time[0];

   return(INIT_SUCCEEDED);
}

//+------------------------------------------------------------------+
//| 验证输入参数                                                     |
//+------------------------------------------------------------------+
bool ValidateInputParameters()
{
   bool isValid = true;

   if(LookbackBars < 1 || LookbackBars > 100)
   {
      Print("错误：回看柱数必须在1-100之间，当前值: ", LookbackBars);
      isValid = false;
   }

   if(MinRange < 0)
   {
      Print("错误：最小波动范围不能为负数，当前值: ", MinRange);
      isValid = false;
   }

   if(BreakoutDistance < 0)
   {
      Print("错误：突破距离不能为负数，当前值: ", BreakoutDistance);
      isValid = false;
   }

   if(StopLossBars < 1 || StopLossBars > 50)
   {
      Print("错误：止损参考柱数必须在1-50之间，当前值: ", StopLossBars);
      isValid = false;
   }

   if(TakeProfit < 0)
   {
      Print("错误：止盈不能为负数，当前值: ", TakeProfit);
      isValid = false;
   }

   if(LotSize <= 0 || LotSize > 100)
   {
      Print("错误：手数必须大于0且不超过100，当前值: ", LotSize);
      isValid = false;
   }

   if(CooldownBars < 0 || CooldownBars > 50)
   {
      Print("错误：冷却K线数量必须在0-50之间，当前值: ", CooldownBars);
      isValid = false;
   }

   if(RecoveryStopBars < 1 || RecoveryStopBars > 50)
   {
      Print("错误：回本单止损参考柱数必须在1-50之间，当前值: ", RecoveryStopBars);
      isValid = false;
   }

   if(TrailingProfit < 0)
   {
      Print("错误：移动止损盈利触发不能为负数，当前值: ", TrailingProfit);
      isValid = false;
   }

   if(TrailingOffset < 0)
   {
      Print("错误：移动止损偏移不能为负数，当前值: ", TrailingOffset);
      isValid = false;
   }

   if(!isValid)
   {
      Print("参数验证失败，EA初始化终止");
   }

   return isValid;
}

void OnDeinit(const int reason)
{
   Print("突破EA已停止");
}

void OnTick()
{
   // 检查是否有新K线产生
   if(Time[0] != lastBarTime)
   {
      lastBarTime = Time[0];
      OnNewBar();
   }

   // 检查同K线盈利平仓阻止开单功能
   if(EnableSameBarBlock) CheckSameBarProfitClose();

   // 实时检测回本功能
   if(EnableRecovery) CheckRecoveryTrigger();



   if(HasOpenPosition())
   {
      if(EnableTrailing) HandleTrailingStop();
      return;
   }

   // 检查是否处于冷却期
   if(isInCooldown)
   {
      return; // 冷却期间不进行新的开仓分析
   }

   // 检查当前K线是否有盈利平仓，如果有则不开单
   if(EnableSameBarBlock && hasProfitCloseThisBar)
   {
      return; // 当前K线有盈利平仓，阻止开单
   }

   AnalyzeBreakout();
}

bool IsNewBar()
{
   datetime barTime = Time[0];
   if(barTime != lastBarTime)
   {
      lastBarTime = barTime;
      return true;
   }
   return false;
}

//+------------------------------------------------------------------+
//| 新K线处理函数                                                    |
//+------------------------------------------------------------------+
void OnNewBar()
{
   // 重置同K线盈利平仓标志
   if(EnableSameBarBlock)
   {
      hasProfitCloseThisBar = false;
      currentBarTime = Time[0];
   }

   // 处理冷却期计数
   if(isInCooldown)
   {
      cooldownBarsCount++;
      Print("冷却期进行中，已冷却K线数: ", cooldownBarsCount, "/", CooldownBars);

      if(cooldownBarsCount >= CooldownBars)
      {
         // 冷却期结束
         isInCooldown = false;
         cooldownStartTime = 0;
         cooldownBarsCount = 0;
         Print("冷却期结束，EA恢复正常运行");
      }
   }
   else
   {
      // 只在新K线时检查盈利平仓触发冷却
      if(EnableCooldown) CheckProfitCloseTrigger();
   }
}

//+------------------------------------------------------------------+
//| 检测盈利平仓触发冷却                                             |
//+------------------------------------------------------------------+
void CheckProfitCloseTrigger()
{
   if(isInCooldown) return; // 已经在冷却期

   // 只检查最近的3个历史订单
   int totalHistory = OrdersHistoryTotal();
   int startIndex = MathMax(0, totalHistory - 3);

   for(int i = totalHistory - 1; i >= startIndex; i--)
   {
      if(OrderSelect(i, SELECT_BY_POS, MODE_HISTORY))
      {
         if(OrderSymbol() == Symbol() &&
            (OrderMagicNumber() == magicNumber || OrderMagicNumber() == recoveryMagicNumber))
         {
            // 检查是否是最近平仓的订单（在当前K线时间范围内）
            if(OrderCloseTime() >= Time[0])
            {
               double profit = OrderProfit() + OrderSwap() + OrderCommission();
               if(profit > 0) // 盈利平仓
               {
                  // 启动冷却期
                  isInCooldown = true;
                  cooldownBarsCount = 0;

                  string orderTypeStr = (OrderMagicNumber() == magicNumber) ? "策略单" : "回本单";
                  Print(orderTypeStr, "盈利平仓，启动冷却期。盈利金额: ", DoubleToStr(profit, 2),
                        " 冷却K线数: ", CooldownBars);
                  break;
               }
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| 检查同K线盈利平仓（阻止开单功能）                                |
//+------------------------------------------------------------------+
void CheckSameBarProfitClose()
{
   // 如果当前K线时间发生变化，重置标志
   if(Time[0] != currentBarTime)
   {
      hasProfitCloseThisBar = false;
      currentBarTime = Time[0];
   }

   // 如果已经检测到当前K线有盈利平仓，无需重复检查
   if(hasProfitCloseThisBar) return;

   // 检查历史订单中是否有在当前K线时间内盈利平仓的订单
   int totalHistory = OrdersHistoryTotal();
   int startIndex = MathMax(0, totalHistory - 3); // 只检查最近3个订单

   for(int i = totalHistory - 1; i >= startIndex; i--)
   {
      if(OrderSelect(i, SELECT_BY_POS, MODE_HISTORY))
      {
         if(OrderSymbol() == Symbol() &&
            (OrderMagicNumber() == magicNumber || OrderMagicNumber() == recoveryMagicNumber))
         {
            // 检查是否在当前K线时间内平仓
            if(OrderCloseTime() >= Time[0] && OrderCloseTime() < Time[0] + Period() * 60)
            {
               double profit = OrderProfit() + OrderSwap() + OrderCommission();
               if(profit > 0) // 盈利平仓
               {
                  hasProfitCloseThisBar = true;
                  string orderTypeStr = (OrderMagicNumber() == magicNumber) ? "策略单" : "回本单";
                  Print("检测到当前K线", orderTypeStr, "盈利平仓，阻止本K线开新单。盈利金额: ",
                        DoubleToStr(profit, 2));
                  break;
               }
            }
         }
      }
   }
}

bool HasOpenPosition()
{
   return HasStrategyPosition() || HasRecoveryPosition();
}

bool HasStrategyPosition()
{
   // 如果有记录的策略单，先验证是否仍然存在
   if(strategyOrder.ticket != -1)
   {
      if(OrderSelect(strategyOrder.ticket, SELECT_BY_TICKET, MODE_TRADES))
      {
         if(OrderSymbol() == Symbol() && OrderMagicNumber() == magicNumber)
         {
            // 策略单仍然存在，更新信息
            strategyOrder.openPrice = OrderOpenPrice();
            strategyOrder.lotSize = OrderLots();
            strategyOrder.orderType = OrderType();
            return true;
         }
      }

      // 策略单已不存在，重置信息
      ResetStrategyOrderInfo();
   }

   // 搜索新的策略单
   for(int i = 0; i < OrdersTotal(); i++)
   {
      if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
      {
         if(OrderSymbol() == Symbol() && OrderMagicNumber() == magicNumber)
         {
            // 找到新的策略单，更新信息
            strategyOrder.ticket = OrderTicket();
            strategyOrder.openPrice = OrderOpenPrice();
            strategyOrder.lotSize = OrderLots();
            strategyOrder.orderType = OrderType();
            strategyOrder.hasRecoveryOrder = false;
            strategyOrder.recoveryTicket = -1;
            return true;
         }
      }
   }

   return false;
}

// 重置策略单信息的辅助函数
void ResetStrategyOrderInfo()
{
   strategyOrder.ticket = -1;
   strategyOrder.openPrice = 0;
   strategyOrder.lotSize = 0;
   strategyOrder.orderType = -1;
   strategyOrder.hasRecoveryOrder = false;
   strategyOrder.recoveryTicket = -1;
}

//+------------------------------------------------------------------+
//| 带重试机制的订单发送函数                                         |
//+------------------------------------------------------------------+
int SendOrderWithRetry(int orderType, double lots, double price, double stopLoss, double takeProfit, string comment, int magic)
{
   int maxRetries = 3;
   int slippage = 3;

   for(int attempt = 1; attempt <= maxRetries; attempt++)
   {
      // 刷新价格
      RefreshRates();

      // 根据订单类型使用正确的价格
      double currentPrice = (orderType == OP_BUY) ? Ask : Bid;

      int ticket = OrderSend(Symbol(), orderType, lots, currentPrice, slippage,
                            stopLoss, takeProfit, comment, magic, 0,
                            (orderType == OP_BUY) ? clrGreen : clrRed);

      if(ticket > 0)
      {
         return ticket; // 成功
      }

      int error = GetLastError();
      Print("订单发送失败，尝试 ", attempt, "/", maxRetries, "，错误代码: ", error);

      // 根据错误类型决定是否重试
      if(error == ERR_TRADE_DISABLED || error == ERR_MARKET_CLOSED ||
         error == ERR_NO_MONEY || error == ERR_TRADE_NOT_ALLOWED)
      {
         Print("严重错误，停止重试: ", error);
         break;
      }

      // 等待一段时间再重试
      if(attempt < maxRetries)
      {
         Sleep(1000); // 等待1秒
      }
   }

   return -1; // 失败
}

bool HasRecoveryPosition()
{
   for(int i = 0; i < OrdersTotal(); i++)
   {
      if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
      {
         if(OrderSymbol() == Symbol() && OrderMagicNumber() == recoveryMagicNumber)
         {
            return true;
         }
      }
   }
   return false;
}

void AnalyzeBreakout()
{
   double highPrice = GetHighestHigh(LookbackBars);
   double lowPrice = GetLowestLow(LookbackBars);

   double range = (highPrice - lowPrice) / Point;

   if(range < MinRange)
   {
      // 优化：放宽突破条件，只需要前1根K线方向正确即可
      bool isPrevious1Bullish = Close[1] > Open[1];  // 前1根是阳线
      bool isPrevious1Bearish = Close[1] < Open[1];  // 前1根是阴线

      // 向上突破：使用Ask价格判断
      if(Ask > highPrice + BreakoutDistance * Point && isPrevious1Bullish)
      {
         OpenBuyOrder(lowPrice);
      }
      // 向下突破：使用Bid价格判断
      else if(Bid < lowPrice - BreakoutDistance * Point && isPrevious1Bearish)
      {
         OpenSellOrder(highPrice);
      }
   }
}

double GetHighestHigh(int bars)
{
   // 检查可用历史数据
   int availableBars = Bars - 1; // 可用的历史K线数量
   int maxBars = MathMin(bars, availableBars - 4); // 确保不超出范围

   if(maxBars < 1) return High[4]; // 数据不足时返回第4根K线的高点

   double highest = High[4];
   for(int i = 4; i <= maxBars + 3; i++) // 调整循环范围
   {
      if(i >= availableBars) break; // 额外安全检查
      if(High[i] > highest)
         highest = High[i];
   }
   return highest;
}

double GetLowestLow(int bars)
{
   // 检查可用历史数据
   int availableBars = Bars - 1; // 可用的历史K线数量
   int maxBars = MathMin(bars, availableBars - 4); // 确保不超出范围

   if(maxBars < 1) return Low[4]; // 数据不足时返回第4根K线的低点

   double lowest = Low[4];
   for(int i = 4; i <= maxBars + 3; i++) // 调整循环范围
   {
      if(i >= availableBars) break; // 额外安全检查
      if(Low[i] < lowest)
         lowest = Low[i];
   }
   return lowest;
}

void OpenBuyOrder(double stopLossPrice)
{
   double stopLoss = GetLowestLow(StopLossBars) - StopLossOffset * Point;
   double takeProfit = Ask + TakeProfit * Point;

   int ticket = SendOrderWithRetry(OP_BUY, LotSize, Ask, stopLoss, takeProfit, "Breakout Buy", magicNumber);

   if(ticket > 0)
   {
      Print("多单开仓成功，订单号: ", ticket, " (前1根K线为阳线)");
   }
   else
   {
      Print("多单开仓失败，已重试多次");
   }
}

void OpenSellOrder(double stopLossPrice)
{
   double stopLoss = GetHighestHigh(StopLossBars) + StopLossOffset * Point;
   double takeProfit = Bid - TakeProfit * Point;

   int ticket = SendOrderWithRetry(OP_SELL, LotSize, Bid, stopLoss, takeProfit, "Breakout Sell", magicNumber);

   if(ticket > 0)
   {
      Print("空单开仓成功，订单号: ", ticket, " (前1根K线为阴线)");
   }
   else
   {
      Print("空单开仓失败，已重试多次");
   }
}

void HandleTrailingStop()
{
   for(int i = 0; i < OrdersTotal(); i++)
   {
      if(OrderSelect(i, SELECT_BY_POS, MODE_TRADES))
      {
         // 修改条件：同时处理策略单和回本单
         if(OrderSymbol() == Symbol() &&
            (OrderMagicNumber() == magicNumber || OrderMagicNumber() == recoveryMagicNumber))
         {
            if(OrderType() == OP_BUY)
            {
               double previousLow = Low[1];
               double openPrice = OrderOpenPrice();
               double triggerProfit = previousLow - openPrice;

               // 当TrailingProfit=0时不受盈利限制，否则检查盈利是否达到触发点
               bool canTrail = (TrailingProfit == 0) || (triggerProfit > TrailingProfit * Point);

               if(canTrail)
               {
                  double newStopLoss = previousLow - TrailingOffset * Point;
                  if(newStopLoss > OrderStopLoss())
                  {
                     bool result = OrderModify(OrderTicket(), openPrice, newStopLoss,
                                              OrderTakeProfit(), 0, clrBlue);
                     if(result)
                     {
                        string orderTypeStr = (OrderMagicNumber() == magicNumber) ? "策略单" : "回本单";
                        Print(orderTypeStr, "多单移动止损: 新止损=", DoubleToStr(newStopLoss, Digits));
                     }
                     else
                     {
                        string orderTypeStr = (OrderMagicNumber() == magicNumber) ? "策略单" : "回本单";
                        Print(orderTypeStr, "多单移动止损失败，错误代码: ", GetLastError());
                     }
                  }
               }
            }
            else if(OrderType() == OP_SELL)
            {
               double previousHigh = High[1];
               double openPrice = OrderOpenPrice();
               double triggerProfit = openPrice - previousHigh;

               // 当TrailingProfit=0时不受盈利限制，否则检查盈利是否达到触发点
               bool canTrail = (TrailingProfit == 0) || (triggerProfit > TrailingProfit * Point);

               if(canTrail)
               {
                  double newStopLoss = previousHigh + TrailingOffset * Point;
                  if(newStopLoss < OrderStopLoss() || OrderStopLoss() == 0)
                  {
                     bool result = OrderModify(OrderTicket(), openPrice, newStopLoss,
                                              OrderTakeProfit(), 0, clrBlue);
                     if(result)
                     {
                        string orderTypeStr = (OrderMagicNumber() == magicNumber) ? "策略单" : "回本单";
                        Print(orderTypeStr, "空单移动止损: 新止损=", DoubleToStr(newStopLoss, Digits));
                     }
                     else
                     {
                        string orderTypeStr = (OrderMagicNumber() == magicNumber) ? "策略单" : "回本单";
                        Print(orderTypeStr, "空单移动止损失败，错误代码: ", GetLastError());
                     }
                  }
               }
            }
         }
      }
   }
}

//+------------------------------------------------------------------+
//| 检测回本触发条件                                                 |
//+------------------------------------------------------------------+
void CheckRecoveryTrigger()
{
   // 检查是否有策略单且还没有回本单
   if(strategyOrder.ticket == -1 || strategyOrder.hasRecoveryOrder) return;

   // 先检查策略单是否还在活跃订单中
   if(OrderSelect(strategyOrder.ticket, SELECT_BY_TICKET, MODE_TRADES))
   {
      // 订单仍然活跃，无需触发回本
      return;
   }

   // 再检查策略单是否在历史订单中
   if(!OrderSelect(strategyOrder.ticket, SELECT_BY_TICKET, MODE_HISTORY)) return;

   // 确认订单已关闭且是亏损的
   if(OrderCloseTime() > 0)
   {
      double profit = OrderProfit() + OrderSwap() + OrderCommission();
      if(profit < 0) // 策略单亏损平仓
      {
         Print("策略单亏损平仓，触发回本功能。亏损金额: ", DoubleToStr(profit, 2));
         OpenRecoveryOrder();
      }
   }
}

//+------------------------------------------------------------------+
//| 开启回本单                                                       |
//+------------------------------------------------------------------+
void OpenRecoveryOrder()
{
   if(strategyOrder.hasRecoveryOrder) return; // 已经有回本单了

   // 计算回本单参数
   int recoveryType = (strategyOrder.orderType == OP_BUY) ? OP_SELL : OP_BUY;
   double recoveryLots = strategyOrder.lotSize;

   double stopLoss, takeProfit;

   if(recoveryType == OP_BUY)
   {
      // 回本多单：止损为前N柱最低点
      stopLoss = GetLowestLow(RecoveryStopBars);

      if(EnableRecoveryTP)
      {
         // 计算策略单的亏损点数
         double lossPoints = MathAbs(strategyOrder.openPrice - OrderClosePrice()) / Point;
         takeProfit = Ask + lossPoints * Point;
      }
      else
      {
         takeProfit = 0; // 不设置止盈
      }

      int ticket = SendOrderWithRetry(OP_BUY, recoveryLots, Ask, stopLoss, takeProfit,
                                     "回本多单", recoveryMagicNumber);

      if(ticket > 0)
      {
         strategyOrder.hasRecoveryOrder = true;
         strategyOrder.recoveryTicket = ticket;
         Print("回本多单开仓成功，订单号: ", ticket, " 手数: ", DoubleToStr(recoveryLots, 2));
      }
   }
   else if(recoveryType == OP_SELL)
   {
      // 回本空单：止损为前N柱最高点
      stopLoss = GetHighestHigh(RecoveryStopBars);

      if(EnableRecoveryTP)
      {
         // 计算策略单的亏损点数
         double lossPoints = MathAbs(strategyOrder.openPrice - OrderClosePrice()) / Point;
         takeProfit = Bid - lossPoints * Point;
      }
      else
      {
         takeProfit = 0; // 不设置止盈
      }

      int ticket = SendOrderWithRetry(OP_SELL, recoveryLots, Bid, stopLoss, takeProfit,
                                     "回本空单", recoveryMagicNumber);

      if(ticket > 0)
      {
         strategyOrder.hasRecoveryOrder = true;
         strategyOrder.recoveryTicket = ticket;
         Print("回本空单开仓成功，订单号: ", ticket, " 手数: ", DoubleToStr(recoveryLots, 2));
      }
   }
}